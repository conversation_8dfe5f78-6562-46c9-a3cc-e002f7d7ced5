import numpy as np
import networkx as nx
import copy  # 导入 copy 模块


# def LIE_two_hop(s_set, G, p):
#     """向量化优化的二跳影响力估计函数
#
#     Args:
#         s_set (set): 种子节点集合
#         G (nx.Graph): 网络图对象
#         p (float): 传播概率
#
#     Returns:
#         float: 基于向量化计算的影响力估计值
#     """
#     # 1. 创建图 G 的副本
#     G_copy = copy.deepcopy(G)  # 使用深拷贝
#
#     # 2. 重新映射节点 ID (在副本上操作)
#     # 将节点重新映射为整数 ID，并保留原始节点 ID
#     G_copy = nx.convert_node_labels_to_integers(G_copy, ordering='default', label_attribute='original_id')
#     num_nodes = G_copy.number_of_nodes()  # 获取图中的节点数量
#
#     # 将输入的种子节点集合转换为映射后的节点 ID
#     remapped_s_set = set()
#     for original_seed in s_set:
#         # 找到原始节点 ID 对应的重新映射后的节点 ID
#         remapped_seed = None
#         for node, data in G_copy.nodes(data=True):
#             if 'original_id' in data and data['original_id'] == original_seed:
#                 remapped_seed = node
#                 break
#         if remapped_seed is not None:
#             remapped_s_set.add(remapped_seed)
#         else:
#             print(f"警告: 原始节点 ID {original_seed} 未在图中找到")
#
#     # 重新设置种子节点集合
#     s_set = remapped_s_set
#
#     # 3. 预生成邻接字典加速查询（节点到邻居集合的映射）
#     adj_dict = {node: set(G_copy.neighbors(node)) for node in G_copy.nodes()}
#
#     # 4. 初始化邻居集合
#     neighbor_s = set()  # 存储一阶邻居
#     neighbor_ss = set()  # 存储二阶邻居
#
#     # 5. 分种子节点计算邻居
#     for node in s_set:
#         # 当前种子节点的一阶邻居（排除种子节点自身）
#         current_neighbors = adj_dict[node] - s_set
#         neighbor_s.update(current_neighbors)  # 合并到全局一阶集合
#
#         # 计算当前种子节点的二阶邻居
#         for neighbor in current_neighbors:
#             # 二阶邻居定义：邻居的邻居，排除种子节点和当前种子的一阶邻居
#             second_neighbors = adj_dict[neighbor] - s_set - current_neighbors
#             neighbor_ss.update(second_neighbors)
#
#     # 6. 向量化计算一阶影响力部分
#     # 生成每个一阶邻居与种子节点的连接数数组
#     connection_counts = np.array([len(adj_dict[n] & s_set) for n in neighbor_s])
#     # 向量化计算激活概率总和 (1 - (1-p)^k)
#     temp_sum = np.sum(1 - (1 - p) ** connection_counts)
#
#     # 7. 计算二阶影响力部分
#     num2 = sum(len(adj_dict[ss] & neighbor_s) for ss in neighbor_ss)  # 二阶到一阶的连接总数
#     # 计算二阶影响力修正项（防止空集合导致的除零错误）
#     temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_s) if neighbor_s else 0
#
#     # 8. 总影响力 = 种子数 + 一阶影响力 + 二阶影响力
#     return len(s_set) + temp_sum + temp_sum_2

""" 优化后的影响力评估 (Expected Diffusion Value)
           graph: NetworkX图对象
           S: 种子节点集合
           p: 传播概率
       Returns:float: 估计的影响力值
       """
def LIE_two_hop(S, graph, p):

    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes()}
    S = set(S)

    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S

    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(adj_dict.get(node, set()) & S)
        influence_sum += 1 - (1 - p) ** num_connections

    return len(S) + influence_sum