#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矢量图输出示例脚本
演示如何使用不同的矢量图格式保存ANFDE算法的结果图表
"""

import matplotlib.pyplot as plt
import numpy as np
import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_sample_plot(vector_format='svg', filename_base='sample_plot'):
    """
    创建示例图表并保存为矢量图格式
    
    Args:
        vector_format: 矢量图格式 ('svg', 'pdf', 'eps')
        filename_base: 文件名基础部分
    """
    
    # 设置matplotlib参数以获得更好的矢量图质量
    plt.rcParams['svg.fonttype'] = 'none'  # 确保SVG中的文字可编辑
    plt.rcParams['pdf.fonttype'] = 42      # 确保PDF中的文字可编辑
    plt.rcParams['ps.fonttype'] = 42       # 确保EPS中的文字可编辑
    
    # 创建示例数据
    generations = range(50)
    lambda_values = 0.5 + 0.3 * np.sin(np.array(generations) * 0.2) * np.exp(-np.array(generations) * 0.02)
    fitness_values = 100 + 50 * (1 - np.exp(-np.array(generations) * 0.1)) + 10 * np.random.normal(0, 0.1, len(generations))
    
    # 变异算子状态（示例）
    mutation_states = []
    for i in generations:
        if i < 10:
            mutation_states.append(2)  # exploration
        elif i < 25:
            mutation_states.append(1)  # exploitation
        elif i < 40:
            mutation_states.append(0)  # convergence
        else:
            mutation_states.append(3)  # escape
    
    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
    
    # 绘制 λ 值
    ax1.plot(generations, lambda_values, label='λ值', color='blue', linewidth=2)
    ax1.set_xlabel('代数', fontsize=12)
    ax1.set_ylabel('λ值', fontsize=12)
    ax1.set_title('λ值随代数变化', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(labelsize=10)
    
    # 绘制适应度
    ax2.plot(generations, fitness_values, label='适应度', color='green', linewidth=2)
    ax2.set_xlabel('代数', fontsize=12)
    ax2.set_ylabel('适应度', fontsize=12)
    ax2.set_title('适应度随代数变化', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(labelsize=10)
    
    # 绘制变异算子状态
    ax3.step(generations, mutation_states, where='post', color='orange', linewidth=2, linestyle='-')
    ax3.scatter(generations, mutation_states, color='red', s=30, zorder=3, alpha=0.8)
    ax3.set_xlabel('Generation', fontsize=12)
    ax3.set_ylabel('Mutation State', fontsize=12)
    ax3.set_title('Mutation Operator States', fontsize=14, fontweight='bold')
    ax3.set_yticks([0, 1, 2, 3])
    ax3.set_yticklabels(['Convergence', 'Exploitation', 'Exploration', 'Escape'])
    ax3.grid(True, linestyle='--', alpha=0.3)
    ax3.tick_params(labelsize=10)
    
    plt.tight_layout()
    
    # 确保输出目录存在
    output_dir = "vector_plots"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存矢量图
    vector_filename = f"{output_dir}/{filename_base}.{vector_format}"
    
    # 根据不同格式设置保存参数
    save_params = {
        'format': vector_format,
        'bbox_inches': 'tight',
        'facecolor': 'white',
        'edgecolor': 'none'
    }
    
    if vector_format == 'svg':
        save_params['dpi'] = 300
    elif vector_format == 'pdf':
        save_params['metadata'] = {
            'Title': 'ANFDE Algorithm Results',
            'Author': 'ANFDE System',
            'Subject': 'Algorithm Performance Analysis',
            'Creator': 'Python matplotlib'
        }
    elif vector_format == 'eps':
        save_params['dpi'] = 300
    
    plt.savefig(vector_filename, **save_params)
    print(f"矢量图已保存为: {vector_filename}")
    
    # 同时保存PNG格式作为备份
    png_filename = f"{output_dir}/{filename_base}.png"
    plt.savefig(png_filename, format='png', dpi=300, bbox_inches='tight')
    print(f"位图已保存为: {png_filename}")
    
    plt.close()

def demonstrate_all_formats():
    """演示所有支持的矢量图格式"""
    
    print("=" * 60)
    print("矢量图格式演示")
    print("=" * 60)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    formats = ['svg', 'pdf', 'eps']
    
    for fmt in formats:
        print(f"\n正在生成 {fmt.upper()} 格式...")
        filename = f"anfde_results_{timestamp}_{fmt}"
        create_sample_plot(vector_format=fmt, filename_base=filename)
    
    print("\n" + "=" * 60)
    print("所有格式生成完成！")
    print("=" * 60)
    
    print("\n矢量图格式说明:")
    print("• SVG: 可缩放矢量图形，适合网页显示，支持交互")
    print("• PDF: 便携式文档格式，适合打印和文档嵌入")
    print("• EPS: 封装PostScript，适合学术论文和专业出版")
    
    print("\n使用建议:")
    print("• 网页展示: 使用 SVG")
    print("• 学术论文: 使用 PDF 或 EPS")
    print("• 印刷出版: 使用 EPS")
    print("• 通用备份: 保留 PNG")

if __name__ == "__main__":
    demonstrate_all_formats()
